<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑用户 - 用户管理系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">编辑用户</h4>
                    </div>
                    <div class="card-body">
                        <form action="${pageContext.request.contextPath}/user/edit" method="post">
                            <input type="hidden" name="id" value="${user.id}">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="${user.name}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱 <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="${user.email}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="age" class="form-label">年龄 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="age" name="age" value="${user.age}" min="1" max="150" required>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="${pageContext.request.contextPath}/user/list" class="btn btn-secondary me-md-2">取消</a>
                                <button type="submit" class="btn btn-primary">更新</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
