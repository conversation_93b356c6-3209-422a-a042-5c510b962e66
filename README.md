# SSM CRUD 用户管理系统

这是一个基于SSM（Spring + Spring MVC + MyBatis）框架的简单CRUD应用程序，使用XML配置文件形式，页面使用Bootstrap进行优化。

## 功能特性

实现单一实体（User）的基本操作：
- ✅ 获取全部数据
- ✅ 根据id查找单一实体
- ✅ 插入新的实体数据
- ✅ 修改实体数据
- ✅ 删除单一实体

## 技术栈

- **后端框架**: Spring 5.3.21 + Spring MVC + MyBatis 3.5.10
- **数据库**: MySQL 8.0
- **连接池**: Druid
- **前端**: JSP + Bootstrap 5.1.3
- **构建工具**: Maven
- **配置方式**: XML配置文件

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/example/
│   │       ├── entity/          # 实体类
│   │       ├── mapper/          # MyBatis Mapper接口
│   │       ├── service/         # 业务逻辑层
│   │       └── controller/      # 控制器层
│   ├── resources/
│   │   ├── mapper/              # MyBatis XML映射文件
│   │   ├── applicationContext.xml    # Spring配置
│   │   ├── spring-mvc.xml            # Spring MVC配置
│   │   ├── mybatis-config.xml        # MyBatis配置
│   │   └── database.properties       # 数据库配置
│   └── webapp/
│       ├── WEB-INF/
│       │   ├── views/user/      # JSP页面
│       │   └── web.xml          # Web应用配置
│       └── index.jsp            # 首页
└── database.sql                 # 数据库初始化脚本
```

## 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Tomcat 9.0+

## 安装部署

### 1. 数据库准备

1. 启动MySQL服务
2. 执行 `database.sql` 脚本创建数据库和表
3. 修改 `src/main/resources/database.properties` 中的数据库连接信息

### 2. 项目构建

```bash
# 编译项目
mvn clean compile

# 打包项目
mvn clean package
```

### 3. 部署运行

1. 将生成的 `ssm-crud-1.0.0.war` 文件部署到Tomcat的webapps目录
2. 启动Tomcat服务器
3. 访问 `http://localhost:8080/ssm-crud-1.0.0/`

## 使用说明

### 主要页面

- **首页**: `/` - 系统欢迎页面
- **用户列表**: `/user/list` - 显示所有用户
- **添加用户**: `/user/add` - 添加新用户
- **编辑用户**: `/user/edit/{id}` - 编辑指定用户
- **查看用户**: `/user/view/{id}` - 查看用户详情
- **删除用户**: `/user/delete/{id}` - 删除指定用户

### 操作流程

1. 访问系统首页，点击"进入用户管理"
2. 在用户列表页面可以查看所有用户
3. 点击"添加用户"按钮添加新用户
4. 点击"查看"按钮查看用户详情
5. 点击"编辑"按钮修改用户信息
6. 点击"删除"按钮删除用户（需确认）

## 配置说明

### 数据库配置
修改 `src/main/resources/database.properties`:
```properties
jdbc.driver=com.mysql.cj.jdbc.Driver
jdbc.url=****************************************************************************************************************************
jdbc.username=root
jdbc.password=123456
```

### 日志配置
项目使用Logback作为日志框架，可根据需要调整日志级别。

## 注意事项

1. 确保MySQL服务正常运行
2. 数据库连接信息配置正确
3. Tomcat版本兼容性
4. 字符编码设置为UTF-8

## 作者信息

- 班级：软件技术13班
- 姓名：赵可欣
- 项目：SSM小项目作业
