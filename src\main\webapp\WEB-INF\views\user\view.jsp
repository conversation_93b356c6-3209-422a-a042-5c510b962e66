<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户详情 - 用户管理系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">用户详情</h4>
                    </div>
                    <div class="card-body">
                        <c:if test="${not empty user}">
                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>ID:</strong>
                                </div>
                                <div class="col-sm-9">
                                    ${user.id}
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>姓名:</strong>
                                </div>
                                <div class="col-sm-9">
                                    ${user.name}
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>邮箱:</strong>
                                </div>
                                <div class="col-sm-9">
                                    ${user.email}
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>年龄:</strong>
                                </div>
                                <div class="col-sm-9">
                                    ${user.age}
                                </div>
                            </div>
                        </c:if>
                        
                        <c:if test="${empty user}">
                            <div class="alert alert-warning" role="alert">
                                用户不存在或已被删除。
                            </div>
                        </c:if>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="${pageContext.request.contextPath}/user/list" class="btn btn-secondary me-md-2">返回列表</a>
                            <c:if test="${not empty user}">
                                <a href="${pageContext.request.contextPath}/user/edit/${user.id}" class="btn btn-warning me-md-2">编辑</a>
                                <a href="${pageContext.request.contextPath}/user/delete/${user.id}" 
                                   class="btn btn-danger"
                                   onclick="return confirm('确定要删除这个用户吗？')">删除</a>
                            </c:if>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
