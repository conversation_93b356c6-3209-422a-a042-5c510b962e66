<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">用户管理系统</h2>
                
                <!-- 操作按钮 -->
                <div class="mb-3">
                    <a href="${pageContext.request.contextPath}/user/add" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 添加用户
                    </a>
                </div>
                
                <!-- 用户列表表格 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">用户列表</h5>
                    </div>
                    <div class="card-body">
                        <c:if test="${empty users}">
                            <div class="alert alert-info" role="alert">
                                暂无用户数据，请添加用户。
                            </div>
                        </c:if>
                        
                        <c:if test="${not empty users}">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>姓名</th>
                                            <th>邮箱</th>
                                            <th>年龄</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <c:forEach var="user" items="${users}">
                                            <tr>
                                                <td>${user.id}</td>
                                                <td>${user.name}</td>
                                                <td>${user.email}</td>
                                                <td>${user.age}</td>
                                                <td>
                                                    <a href="${pageContext.request.contextPath}/user/view/${user.id}" 
                                                       class="btn btn-info btn-sm">查看</a>
                                                    <a href="${pageContext.request.contextPath}/user/edit/${user.id}" 
                                                       class="btn btn-warning btn-sm">编辑</a>
                                                    <a href="${pageContext.request.contextPath}/user/delete/${user.id}" 
                                                       class="btn btn-danger btn-sm"
                                                       onclick="return confirm('确定要删除这个用户吗？')">删除</a>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </c:if>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
