<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.mapper.UserMapper">
    
    <!-- 结果映射 -->
    <resultMap id="UserResultMap" type="com.example.entity.User">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="email" column="email"/>
        <result property="age" column="age"/>
    </resultMap>
    
    <!-- 获取全部用户数据 -->
    <select id="findAll" resultMap="UserResultMap">
        SELECT id, name, email, age FROM user ORDER BY id
    </select>
    
    <!-- 根据id查找单一用户 -->
    <select id="findById" parameterType="int" resultMap="UserResultMap">
        SELECT id, name, email, age FROM user WHERE id = #{id}
    </select>
    
    <!-- 插入新的用户数据 -->
    <insert id="insert" parameterType="com.example.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (name, email, age) VALUES (#{name}, #{email}, #{age})
    </insert>
    
    <!-- 修改用户数据 -->
    <update id="update" parameterType="com.example.entity.User">
        UPDATE user SET name = #{name}, email = #{email}, age = #{age} WHERE id = #{id}
    </update>
    
    <!-- 删除单一用户 -->
    <delete id="deleteById" parameterType="int">
        DELETE FROM user WHERE id = #{id}
    </delete>
    
</mapper>
