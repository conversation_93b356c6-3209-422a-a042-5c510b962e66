<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body text-center">
                        <h1 class="card-title">欢迎使用用户管理系统</h1>
                        <p class="card-text">这是一个基于SSM框架的简单CRUD应用程序</p>
                        <p class="card-text">
                            <small class="text-muted">Spring + Spring MVC + MyBatis</small>
                        </p>
                        <a href="${pageContext.request.contextPath}/user/list" class="btn btn-primary btn-lg">
                            进入用户管理
                        </a>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">功能说明</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">✅ 获取全部用户数据</li>
                            <li class="list-group-item">✅ 根据ID查找单一用户</li>
                            <li class="list-group-item">✅ 插入新的用户数据</li>
                            <li class="list-group-item">✅ 修改用户数据</li>
                            <li class="list-group-item">✅ 删除单一用户</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
