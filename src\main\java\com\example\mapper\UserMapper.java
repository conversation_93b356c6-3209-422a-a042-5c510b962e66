package com.example.mapper;

import com.example.entity.User;
import java.util.List;

/**
 * 用户数据访问接口
 */
public interface UserMapper {
    
    /**
     * 获取全部用户数据
     * @return 用户列表
     */
    List<User> findAll();
    
    /**
     * 根据id查找单一用户
     * @param id 用户ID
     * @return 用户对象
     */
    User findById(Integer id);
    
    /**
     * 插入新的用户数据
     * @param user 用户对象
     * @return 影响行数
     */
    int insert(User user);
    
    /**
     * 修改用户数据
     * @param user 用户对象
     * @return 影响行数
     */
    int update(User user);
    
    /**
     * 删除单一用户
     * @param id 用户ID
     * @return 影响行数
     */
    int deleteById(Integer id);
}
