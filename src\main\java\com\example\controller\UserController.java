package com.example.controller;

import com.example.entity.User;
import com.example.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器
 */
@Controller
@RequestMapping("/user")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取全部用户数据 - 显示用户列表页面
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public String list(Model model) {
        List<User> users = userService.findAll();
        model.addAttribute("users", users);
        return "user/list";
    }
    
    /**
     * 显示添加用户页面
     */
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    public String showAddForm(Model model) {
        model.addAttribute("user", new User());
        return "user/add";
    }
    
    /**
     * 插入新的用户数据
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public String add(@ModelAttribute User user) {
        userService.save(user);
        return "redirect:/user/list";
    }
    
    /**
     * 显示编辑用户页面
     */
    @RequestMapping(value = "/edit/{id}", method = RequestMethod.GET)
    public String showEditForm(@PathVariable Integer id, Model model) {
        User user = userService.findById(id);
        model.addAttribute("user", user);
        return "user/edit";
    }
    
    /**
     * 修改用户数据
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public String update(@ModelAttribute User user) {
        userService.update(user);
        return "redirect:/user/list";
    }
    
    /**
     * 删除单一用户
     */
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.GET)
    public String delete(@PathVariable Integer id) {
        userService.deleteById(id);
        return "redirect:/user/list";
    }
    
    /**
     * 根据id查找单一用户 - 显示用户详情页面
     */
    @RequestMapping(value = "/view/{id}", method = RequestMethod.GET)
    public String view(@PathVariable Integer id, Model model) {
        User user = userService.findById(id);
        model.addAttribute("user", user);
        return "user/view";
    }
}
